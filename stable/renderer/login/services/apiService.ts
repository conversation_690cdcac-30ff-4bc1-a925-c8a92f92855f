/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable @typescript-eslint/no-throw-literal */
/* eslint-disable no-useless-catch */
/**
 * API服务配置
 * 统一封装HTTP请求方法和API配置
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data?: T;
}

// 极验初始化响应接口
export interface GeetestInitResponse {
  static_servers: string[];
  slide: string;
  challenge: string;
  api_server: string;
  type: string;
  fullpage: string;
  click: string;
  cid: string;
}

// 登录响应接口
export interface LoginResponse {
  token: string;
  expireTimeSeconds: number;
  userID: string;
}

// 两步验证响应接口
export interface TwoStepVerifyResponse {
  mobile: string;
}

// 设备信息接口
export interface DeviceInfo {
  platformID: number;
  deviceID: string;
}

class ApiService {
  private readonly axiosInstance: AxiosInstance;

  private readonly baseURL: string;

  constructor() {
    // 测试环境API地址
    this.baseURL =
      'http://linkapp-test.htsc.com.cn:48086/linkFlowService/coco-bff';

    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors() {
    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        console.log(
          'API Request:',
          config.method?.toUpperCase(),
          config.url,
          config.data
        );
        return config;
      },
      (error) => {
        console.error('Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log('API Response:', response.status, response.data);
        return response;
      },
      (error) => {
        console.error('Response Error:', error);

        // 网络错误处理
        if (!error.response) {
          return Promise.reject({
            code: -1,
            msg: '网络连接失败，请检查网络设置',
            data: null,
          });
        }

        // HTTP状态码错误处理
        const { status, data } = error.response;
        return Promise.reject({
          code: status,
          msg: data?.msg || `请求失败 (${status})`,
          data: data?.data || null,
        });
      }
    );
  }

  /**
   * 生成设备ID
   */
  private generateDeviceID(): string {
    // 简单的设备ID生成逻辑，实际项目中可能需要更复杂的实现
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2);
    return `${timestamp}_${random}`;
  }

  /**
   * 获取设备信息
   */
  private getDeviceInfo(): DeviceInfo {
    return {
      platformID: 1, // 1表示PC端
      deviceID: this.generateDeviceID(),
    };
  }

  /**
   * 极验初始化接口
   */
  async initGeetest(): Promise<ApiResponse<GeetestInitResponse>> {
    try {
      const deviceInfo = this.getDeviceInfo();
      const response = await this.axiosInstance.post<
        ApiResponse<GeetestInitResponse>
      >('/linkflow/geetest/register', deviceInfo);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 用户登录接口
   */
  async login(params: {
    employeeCode: string;
    password: string;
    challenge: string;
  }): Promise<ApiResponse<LoginResponse | TwoStepVerifyResponse>> {
    try {
      const deviceInfo = this.getDeviceInfo();
      const loginData = {
        ...deviceInfo,
        ...params,
      };

      const response = await this.axiosInstance.post<
        ApiResponse<LoginResponse>
      >('/linkflow/user/login', loginData);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 通用GET请求
   */
  async get<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.get<ApiResponse<T>>(
        url,
        config
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 通用POST请求
   */
  async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.post<ApiResponse<T>>(
        url,
        data,
        config
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 通用PUT请求
   */
  async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.put<ApiResponse<T>>(
        url,
        data,
        config
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 通用DELETE请求
   */
  async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.delete<ApiResponse<T>>(
        url,
        config
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 更新基础URL（用于环境切换）
   */
  updateBaseURL(newBaseURL: string) {
    this.axiosInstance.defaults.baseURL = newBaseURL;
  }

  /**
   * 设置认证token
   */
  setAuthToken(token: string) {
    this.axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`;
  }

  /**
   * 清除认证token
   */
  clearAuthToken() {
    delete this.axiosInstance.defaults.headers.common.Authorization;
  }

  /**
   * 发送验证码接口
   */
  async resendCode(params: {
    platformID: number;
    deviceID: string;
    employeeCode: string;
    password: string;
    challenge: string;
  }): Promise<ApiResponse> {
    try {
      const response = await this.axiosInstance.post<ApiResponse>(
        '/linkflow/user/resendCode',
        params
      );
      // 检查返回的code码
      if (response.data.code !== 0) {
        throw {
          code: response.data.code,
          msg: response.data.msg || '发送验证码失败',
          data: response.data.data,
        };
      }
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 验证码登录接口
   */
  async loginWithCode(params: {
    platformID: number;
    deviceID: string;
    employeeCode: string;
    password: string;
    code: string;
  }): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await this.axiosInstance.post<
        ApiResponse<LoginResponse>
      >('/linkflow/user/loginWithCode', params);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}

// 导出单例实例
export const apiService = new ApiService();
export default apiService;
