/**
 * 登录模块工具函数
 */

/**
 * 检查是否为HTTPS环境
 * 用于极验验证的https配置
 */
export function getIsHyperProduct(): boolean {
  // 检查当前协议是否为https
  if (typeof window !== 'undefined') {
    return window.location.protocol === 'https:';
  }

  // 在Node.js环境中，默认返回false
  return false;
}

/**
 * 生成随机字符串
 * @param length 字符串长度
 * @returns 随机字符串
 */
export function generateRandomString(length = 16): string {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否为有效邮箱
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证用户名格式
 * @param username 用户名
 * @returns 是否为有效用户名
 */
export function isValidUsername(username: string): boolean {
  // 用户名：2-20位字母、数字、下划线、连字符
  const usernameRegex = /^[a-zA-Z0-9_-]{2,20}$/;
  return usernameRegex.test(username);
}

/**
 * 验证工号格式
 * @param employeeCode 工号
 * @returns 是否为有效工号
 */
export function isValidEmployeeCode(employeeCode: string): boolean {
  // 工号：通常为6位数字，但这里放宽一些限制
  const employeeCodeRegex = /^[0-9]{4,8}$/;
  return employeeCodeRegex.test(employeeCode);
}

/**
 * 验证密码强度
 * @param password 密码
 * @returns 密码强度等级 (weak, medium, strong)
 */
export function getPasswordStrength(
  password: string
): 'weak' | 'medium' | 'strong' {
  if (password.length < 6) {
    return 'weak';
  }

  let score = 0;

  // 长度加分
  if (password.length >= 8) {
    score += 1;
  }
  if (password.length >= 12) {
    score += 1;
  }

  // 包含小写字母
  if (/[a-z]/.test(password)) {
    score += 1;
  }

  // 包含大写字母
  if (/[A-Z]/.test(password)) {
    score += 1;
  }

  // 包含数字
  if (/[0-9]/.test(password)) {
    score += 1;
  }

  // 包含特殊字符
  if (/[^a-zA-Z0-9]/.test(password)) {
    score += 1;
  }

  if (score <= 2) {
    return 'weak';
  }
  if (score <= 4) {
    return 'medium';
  }
  return 'strong';
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;

  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}

/**
 * 格式化错误消息
 * @param error 错误对象
 * @returns 格式化后的错误消息
 */
export function formatErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.msg) {
    return error.msg;
  }

  if (error?.message) {
    return error.message;
  }

  if (error?.data?.msg) {
    return error.data.msg;
  }

  return '未知错误，请稍后重试';
}

/**
 * 存储到本地存储
 * @param key 键名
 * @param value 值
 */
export function setLocalStorage(key: string, value: any): void {
  try {
    const serializedValue = JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error('Failed to save to localStorage:', error);
  }
}

/**
 * 从本地存储获取
 * @param key 键名
 * @param defaultValue 默认值
 * @returns 存储的值或默认值
 */
export function getLocalStorage<T>(key: string, defaultValue: T): T {
  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      return defaultValue;
    }
    return JSON.parse(item);
  } catch (error) {
    console.error('Failed to get from localStorage:', error);
    return defaultValue;
  }
}

/**
 * 从本地存储删除
 * @param key 键名
 */
export function removeLocalStorage(key: string): void {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Failed to remove from localStorage:', error);
  }
}

/**
 * 检查网络连接状态
 * @returns 是否在线
 */
export function isOnline(): boolean {
  if (typeof navigator !== 'undefined') {
    return navigator.onLine;
  }
  return true; // 在Node.js环境中默认认为在线
}
