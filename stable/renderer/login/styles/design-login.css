/**
 * 根据设计稿实现的登录组件样式
 * 严格按照设计稿的视觉规范
 */

/* 全局样式 - 防止滚动条 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Helvetica Neue", sans-serif;
}

.design-login-container {
  position: relative;
  width: 320px;
  height: 480px;
  margin: 0;
  padding: 24px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 12%);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
}

/* 左上角二维码占位区域 */
.qr-code-placeholder {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  border-radius: 8px;
  opacity: 0.7;
}

.qr-squares {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 2px;
  width: 16px;
  height: 16px;
}

.qr-square {
  background: #4f46e5;
  border-radius: 1px;
}

/* 右上角关闭按钮 */
.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
  -webkit-app-region: no-drag;
  z-index: 10;
}

.close-button:hover {
  background: #f3f4f6;
  color: #6b7280;
}

/* 网络异常提示 */
.design-error-alert {
  display: flex;
  align-items: center;
  margin-top: 30px;
  margin-bottom: 24px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
}

.design-error-alert .error-icon {
  color: #dc2626;
  margin-right: 8px;
  font-size: 16px;
  font-weight: bold;
}

.design-error-alert .error-message {
  color: #dc2626;
  flex: 1;
}

/* 登录表单 */
.design-login-form {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 标题样式 */
.design-login-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  text-align: center;
  margin-bottom: 32px;
  margin-top: 30px;
  flex-shrink: 0;
  -webkit-app-region: drag;
}

/* 输入框组 */
.design-input-group {
  margin-bottom: 16px;
  -webkit-app-region: no-drag;
}

.design-input {
  width: 100%;
  height: 36px;
  padding: 0 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  color: #111827;
  background-color: #ffffff;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.design-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 10%);
}

.design-input.error {
  border-color: #ef4444;
}

.design-input::placeholder {
  color: #d1d5db;
}

/* 密码输入框包装器 */
.design-password-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.design-password-input {
  padding-right: 50px;
}

.design-password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  transition: color 0.2s ease;
  border-radius: 4px;
}

.design-password-toggle:hover {
  color: #6b7280;
  background: #f9fafb;
}

.design-password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.design-password-toggle .eye-icon {
  font-size: 18px;
  user-select: none;
}

/* 字段错误提示 */
.design-field-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 4px;
}

/* 登录按钮 */
.design-login-button {
  width: 100%;
  height: 36px;
  background: #0084ff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 100px;
  margin-bottom: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 132, 255, 30%);
  -webkit-app-region: no-drag;
}

.design-login-button:hover:not(:disabled) {
  background: #0070e6;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 132, 255, 40%);
}

.design-login-button:active:not(:disabled) {
  transform: translateY(0);
}

.design-login-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 自动登录复选框 */
.design-auto-login {
  display: flex;
  justify-content: center;
  margin-top: 8px;
  -webkit-app-region: no-drag;
}

.design-checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 14px;
  color: #374151;
}

.design-checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.design-checkbox-custom {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: #ffffff;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  margin-right: 8px;
  transition: all 0.2s ease;
}

.design-checkbox-label:hover .design-checkbox-custom {
  border-color: #3b82f6;
}

.design-checkbox-input:checked + .design-checkbox-custom {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.design-checkbox-input:checked + .design-checkbox-custom::after {
  content: "";
  position: absolute;
  display: block;
  left: 5px;
  top: 1px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.design-checkbox-text {
  font-size: 14px;
  color: #374151;
}

/* 外部容器样式 - 用于整个登录页面 */
.design-login-app {
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Helvetica Neue", sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
}
