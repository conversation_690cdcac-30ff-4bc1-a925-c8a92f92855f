/**
 * 手机号验证码界面样式
 * 根据设计稿实现的独立全屏页面布局
 */

/* 全屏应用容器 */
.phone-verify-app {
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Helvetica Neue", sans-serif;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
}

/* 验证码卡片容器 */
.phone-verify-card {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 12%);
  padding: 24px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  animation: slide-in-up 0.3s ease-out;
}

/* 顶部导航区域 */
.phone-verify-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  background: transparent;
  flex-shrink: 0;
}

/* 返回按钮 */
.nav-back-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #333333;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.nav-back-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.nav-back-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.back-arrow {
  font-size: 16px;
  margin-right: 4px;
  height: 16px;
}

.back-text {
  font-size: 14px;
}

/* 关闭按钮 */
.nav-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  color: #999999;
  font-size: 24px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.nav-close-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
  color: #666666;
}

.nav-close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 标题与说明文字区域 */
.phone-verify-header {
  text-align: left;
  padding: 0;
  margin-top: 46px;
  flex-shrink: 0;
}

.verify-title {
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  margin: 0 0 12px;
  line-height: 1.2;
}

.verify-description {
  font-size: 14px;
  color: #000000;
  margin: 0;
  line-height: 1.4;
}

.phone-number {
  color: #1d1d1d;
  font-weight: 600;
}

/* 验证码输入区域 */
.phone-verify-form {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.verify-input-group {
  margin-top: 28px;
  margin-bottom: 24px;
}

.verify-code-input {
}

/* 验证码输入框 */
.verify-input {
  height: 36px;
  padding: 0 100px 0 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #333333;
  background-color: #ffffff;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.verify-input:focus {
  outline: none;
  border-color: #0074e2;
  box-shadow: 0 0 0 2px rgba(0, 116, 226, 10%);
}

.verify-input.error {
  border-color: #dc2626;
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 10%);
}

.verify-input::placeholder {
  color: #9ca3af;
}

/* 获取验证码按钮 */
.get-code-btn {
  position: absolute;
  right: 24px;
  background: none;
  border: none;
  color: #0074e2;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.get-code-btn:hover:not(:disabled) {
  background-color: rgba(0, 116, 226, 10%);
}

.get-code-btn:disabled {
  color: #9ca3af;
  cursor: not-allowed;
  background: none;
}

/* 错误提示文字 */
.error-text {
  display: block;
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 4px;
}

/* 确定按钮区域 */
.phone-verify-actions {
  display: flex;
  justify-content: center;
  margin-top: auto;
  padding-top: 40px;
}

/* 确定按钮 */
.confirm-btn {
  width: 270px;
  height: 36px;
  background: #0084ff;
  border: none;
  border-radius: 6px;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-btn:hover:not(:disabled) {
  background: #0066cc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 132, 255, 30%);
}

.confirm-btn:active:not(:disabled) {
  transform: translateY(0);
}

.confirm-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 动画效果 */
@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
