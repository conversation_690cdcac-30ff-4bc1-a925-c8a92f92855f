/**
 * This file will automatically be loaded by webpack and run in the "renderer" context.
 * To learn more about the differences between the "main" and the "renderer" context in
 * Electron, visit:
 *
 * https://electronjs.org/docs/latest/tutorial/process-model
 *
 * By default, Node.js integration in this file is disabled. When enabling Node.js integration
 * in a renderer process, please be aware of potential security implications. You can read
 * more about security risks here:
 *
 * https://electronjs.org/docs/tutorial/security
 *
 * To enable Node.js integration in this file, open up `main.js` and enable the `nodeIntegration`
 * flag:
 *
 * ```
 *  // Create the browser window.
 *  mainWindow = new BrowserWindow({
 *    width: 800,
 *    height: 600,
 *    webPreferences: {
 *      nodeIntegration: true
 *    }
 *  });
 * ```
 */

// 引入登录模块
import './login';
import '../../public/js/gt';

const getCurrentHref = () => {
  if (process.env.SERVER === 'development') {
    return 'http://localhost:8001/linkflow/chat?debug=true';
    // return 'http://eipdev.htsc.com.cn/linkflow/chat';
  } else if (process.env.SERVER === 'sit') {
    return 'http://eipdev.htsc.com.cn/linkflow/chat';
  } else if (process.env.SERVER === 'uat') {
    return 'http://eipsit.htsc.com.cn/linkflow/chat?debug=true';
  } else if (process.env.SERVER === 'prod') {
    return 'http://eip.htsc.com.cn/linkflow/chat';
  } else if (process.env.SERVER === 'lite') {
    return 'http://eiplite.htsc.com.cn/linkflow/chat';
  } else {
    return 'http://eipsit.htsc.com.cn/linkflow/chat';
  }
};

// 检查登录状态的函数
const checkLoginStatus = (): Promise<boolean> => {
  return new Promise((resolve) => {
    // 使用登录模块检查状态
    const isLoggedIn = (window as any).LoginModule?.checkLoginStatus() || false;
    resolve(isLoggedIn);
  });
};

// 显示登录页面
const showLoginPage = () => {
  document.body.innerHTML = `
    <div id="login-container">
      <div id="login-form">
        <h2>用户登录</h2>
        <div id="login-content">
          <!-- 这里将由你的登录页面逻辑填充 -->
          <p>正在加载登录页面...</p>
        </div>
        <button id="login-btn" onclick="handleLogin()">登录</button>
      </div>
    </div>
  `;

  // 触发自定义登录页面初始化事件
  window.dispatchEvent(new CustomEvent('loginPageReady'));
};

// 处理登录成功后的跳转
const handleLoginSuccess = () => {
  window.location.href = getCurrentHref();
};

// 暴露给全局的登录处理函数
(window as any).handleLogin = () => {
  // 这里将由你的登录逻辑实现
  // 登录成功后调用 handleLoginSuccess()
  console.log('Login button clicked - implement your login logic here');
};

// 暴露登录成功处理函数给外部调用
(window as any).handleLoginSuccess = handleLoginSuccess;

if (window.location.href.includes('?retry=true')) {
  const tipsImg = document.createElement('img');
  tipsImg.src =
    'data:image/png;base64,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';

  const tipsContent1 = document.createElement('div');
  tipsContent1.innerText = '网络不稳定，简富加载失败，正在积极为您重试。。。';

  const tipsContent2 = document.createElement('div');
  tipsContent2.innerText =
    '如果一直不成功，请您尝试切换WIFI或者有线网络后重新打开简富！';

  const retryBtn = document.createElement('button');
  retryBtn.innerText = '立即重试';
  retryBtn.onclick = () => {
    window.location.href = getCurrentHref();
  };

  const tipsDiv = document.createElement('div');

  tipsDiv.appendChild(tipsImg);
  tipsDiv.appendChild(tipsContent1);
  tipsDiv.appendChild(tipsContent2);
  tipsDiv.appendChild(retryBtn);

  document.body.appendChild(tipsDiv);
}

// 修改启动逻辑：先检查登录状态
const initializeApp = async () => {
  const isLoggedIn = await checkLoginStatus();
  debugger;
  console.log('isLoggedIn', isLoggedIn);

  if (isLoggedIn) {
    // 已登录，直接跳转到目标页面
    setTimeout(
      () => {
        window.location.href = getCurrentHref();
      },
      process.env.NODE_ENV === 'production' ? 3000 : 0
    );
  } else {
    // 未登录，显示登录页面
    showLoginPage();
  }
};

// 启动应用
initializeApp();
