import type { Configuration } from 'webpack';
import path from 'path';
import { plugins } from './webpack.plugins';
import { rules } from './webpack.rules';

const resolve = (dir: string) => path.resolve(__dirname, dir);

rules.push({
  test: /\.css$/,
  use: [{ loader: 'style-loader' }, { loader: 'css-loader' }],
});

export const rendererConfig: Configuration = {
  module: {
    rules,
  },
  plugins,
  resolve: {
    extensions: ['.js', '.ts', '.jsx', '.tsx', '.css'],
    alias: {
      '@common': resolve('../../src/common'),
      '@utils': resolve('../../src/utils'),
      '@globalState': resolve('../../src/globalState'),
      '@lightenModule': resolve('../../src/lightenModule'),
      '@htElectronSDK': resolve('../../src/htElectronSDK'),
      '@lightenSDK': resolve('../../src/lightenSDK'),
      '@login': resolve('../../stable/renderer/login'),
      '@public': resolve('../../public'),
    },
  },
  // 如果渲染进程用到electron，编译时先忽略
  externals: {
    electron: 'require("electron")',
  },
};
